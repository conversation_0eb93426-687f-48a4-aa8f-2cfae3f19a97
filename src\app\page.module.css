.container {
  padding: 0 2rem;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.main {
  min-height: 100vh;
  padding: 4rem 0;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.title {
  margin: 0 0 3rem 0;
  line-height: 1.15;
  font-size: 4rem;
  color: white;
  text-align: center;
}

.grid {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  flex-wrap: wrap;
  max-width: 1200px;
  gap: 2rem;
}

.card {
  margin: 1rem;
  padding: 1.5rem;
  text-align: center;
  color: inherit;
  text-decoration: none;
  border: 1px solid #eaeaea;
  border-radius: 10px;
  transition: color 0.15s ease, border-color 0.15s ease;
  max-width: 300px;
  background: white;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card:hover {
  color: #0070f3;
  border-color: #0070f3;
}

.card h2 {
  margin: 0 0 1rem 0;
  font-size: 1.5rem;
  color: #333;
}

.status {
  font-size: 1.2rem;
  font-weight: bold;
  padding: 1rem;
  border-radius: 8px;
  margin: 1rem 0;
}

.alert {
  background-color: #ffebee;
  color: #c62828;
  border: 2px solid #ef5350;
}

.safe {
  background-color: #e8f5e8;
  color: #2e7d32;
  border: 2px solid #4caf50;
}

.on {
  background-color: #fff3e0;
  color: #ef6c00;
  border: 2px solid #ff9800;
}

.off {
  background-color: #f5f5f5;
  color: #666;
  border: 2px solid #bdbdbd;
}

.button {
  background-color: #0070f3;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  margin: 0.5rem;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.2s;
}

.button:hover {
  background-color: #0051a2;
}

.buttonGroup {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .title {
    font-size: 2.5rem;
  }
  
  .grid {
    flex-direction: column;
    align-items: center;
  }
  
  .card {
    margin: 0.5rem;
    max-width: 100%;
  }
}