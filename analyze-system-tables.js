import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.NEXT_SUPABASE_SERVICE_ROLE_KEY

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function analyzeSystemTables() {
  console.log('🔍 Analyzing which systems have dedicated tables...\n')
  
  try {
    // Get all systems from the database
    const { data: systems, error: systemsError } = await supabase
      .from('systems')
      .select('*')
      .order('type')
    
    if (systemsError) {
      console.error('Error fetching systems:', systemsError)
      return
    }
    
    console.log(`📋 Found ${systems.length} systems in database:\n`)
    
    // Get unique system types
    const systemTypes = [...new Set(systems.map(s => s.type))]
    
    // List of potential system tables to check
    const potentialTables = [
      'lighting_system',
      'fan_control', 
      'door_access',
      'garage_control',
      'smoke_alarm',
      'window_control',
      'rain_detection',
      'security_system'
    ]
    
    console.log('🔍 System Types Found:')
    systemTypes.forEach(type => {
      const systemsOfType = systems.filter(s => s.type === type)
      console.log(`   • ${type} (${systemsOfType.length} instances)`)
      systemsOfType.forEach(system => {
        console.log(`     - ${system.name}`)
      })
    })
    
    console.log('\n📊 Checking for dedicated system tables...\n')
    
    const tableResults = {}
    
    for (const tableName of potentialTables) {
      try {
        const { data, error } = await supabase
          .from(tableName)
          .select('*')
          .limit(1)
        
        if (error) {
          tableResults[tableName] = { exists: false, error: error.message }
        } else {
          tableResults[tableName] = { exists: true, recordCount: data?.length || 0 }
          
          // Get sample record to see structure
          if (data && data.length > 0) {
            tableResults[tableName].sampleColumns = Object.keys(data[0])
          }
        }
      } catch (err) {
        tableResults[tableName] = { exists: false, error: err.message }
      }
    }
    
    // Analyze results
    console.log('📋 System Table Analysis:')
    console.log('✅ = Table exists | ❌ = Table missing\n')
    
    const existingTables = []
    const missingTables = []
    
    Object.entries(tableResults).forEach(([tableName, result]) => {
      if (result.exists) {
        console.log(`✅ ${tableName}`)
        if (result.sampleColumns) {
          console.log(`   Columns: ${result.sampleColumns.join(', ')}`)
        }
        existingTables.push(tableName)
      } else {
        console.log(`❌ ${tableName} - ${result.error}`)
        missingTables.push(tableName)
      }
    })
    
    console.log('\n🎯 Pattern Analysis:')
    console.log(`   • Systems with dedicated tables: ${existingTables.length}`)
    console.log(`   • Systems without dedicated tables: ${missingTables.length}`)
    
    console.log('\n📝 Existing System Tables:')
    existingTables.forEach(table => {
      console.log(`   • ${table}`)
    })
    
    console.log('\n❓ Missing System Tables:')
    missingTables.forEach(table => {
      console.log(`   • ${table}`)
    })
    
    // Check which system types correspond to existing tables
    console.log('\n🔗 System Type → Table Mapping:')
    systemTypes.forEach(systemType => {
      const potentialTableName = systemType.replace('_', '_')
      const hasTable = existingTables.includes(potentialTableName)
      console.log(`   • ${systemType} → ${potentialTableName} ${hasTable ? '✅' : '❌'}`)
    })
    
    console.log('\n💡 Conclusion:')
    console.log('   Based on the analysis, it appears that:')
    if (existingTables.length > 0) {
      console.log(`   • Some systems DO have dedicated tables (${existingTables.join(', ')})`)
    }
    if (missingTables.length > 0) {
      console.log(`   • Some systems do NOT have dedicated tables`)
    }
    console.log('   • The pattern seems to be: complex systems with configuration get tables')
    console.log('   • Simple systems just use devices table with properties/current_state')
    
  } catch (error) {
    console.error('❌ Analysis failed:', error.message)
  }
}

analyzeSystemTables().catch(console.error)
