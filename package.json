{"name": "test", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@supabase/supabase-js": "^2.50.0", "dotenv": "^16.5.0", "next": "15.3.3", "pg": "^8.16.0", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.3", "postcss": "^8.5.4", "tailwindcss": "^3.4.17"}}