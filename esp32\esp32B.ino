#include <ESP32Servo.h>
#include <Keypad.h>
#include <Wire.h>
#include <hd44780.h>
#include <hd44780ioClass/hd44780_I2Cexp.h>

// LCD setup
hd44780_I2Cexp lcd; // Auto detects I2C address

// Pin definitions - optimized for ESP32 WROOM-32 30-pin
const int outsideIrSensorPin = 32; // Outside IR sensor (input only pin)
const int insideIrSensorPin = 35;  // Inside IR sensor (input only pin)
const int mainDoorServoPin = 18;   // Main door servo pin (180° servo, PWM capable)
const int garageDoorServoPin = 19; // Garage door servo pin (360° servo, PWM capable)
const int buzzerPin = 5;           // Buzzer pin (output)
const int mq2Pin = 34;             // MQ2 gas sensor (ADC1, input only)

// I2C pins for LCD (default ESP32 pins)
// SDA = GPIO 21, SCL = GPIO 22

// Keypad pins
const byte ROWS = 4;
const byte COLS = 3;
char keys[ROWS][COLS] = {
    {'1', '2', '3'},
    {'4', '5', '6'},
    {'7', '8', '9'},
    {'*', '0', '#'}};
byte rowPins[ROWS] = {13, 12, 14, 27}; // Row pins
byte colPins[COLS] = {26, 25, 33};     // Column pins

Keypad keypad = Keypad(makeKeymap(keys), rowPins, colPins, ROWS, COLS);
Servo mainDoorServo;   // 180° servo for main door
Servo garageDoorServo; // 360° servo for garage door

// System state variables
bool mainDoorIsOpen = false;
bool garageDoorIsOpen = false;
bool systemLocked = true;
bool gasAlarmActive = false;
unsigned long lastDisplayUpdate = 0;
unsigned long statusMessageTime = 0;
String statusMessage = "";
bool showingStatus = false;

// Security system variables
const String correctPassword = "2309";
String inputPassword = "";
int attemptCount = 0;
const int maxAttempts = 3;

// IR sensor variables
int outsideSensorValue = 0;
int insideSensorValue = 0;
bool outsideObjectDetected = false;
bool insideObjectDetected = false;

// Gas sensor variables
const int gasThreshold = 500;
const int buzzDuration = 10000; // 10 seconds in milliseconds
int mq2Value = 0;
unsigned long buzzerStartTime = 0;
bool buzzerActive = false;

void setup()
{
    Serial.begin(115200);

    // Initialize pins
    pinMode(outsideIrSensorPin, INPUT);
    pinMode(insideIrSensorPin, INPUT);
    pinMode(buzzerPin, OUTPUT);
    digitalWrite(buzzerPin, LOW);

    // Initialize servos
    mainDoorServo.attach(mainDoorServoPin);
    mainDoorServo.write(0); // Main door closed/locked (180° servo)
    mainDoorIsOpen = false;

    garageDoorServo.attach(garageDoorServoPin);
    garageDoorServo.write(90); // Garage door stopped (360° servo)
    garageDoorIsOpen = false;

    // Initialize LCD
    int status = lcd.begin(16, 2);
    if (status)
    {
        Serial.print("LCD init failed: ");
        Serial.println(status);
    }
    else
    {
        lcd.clear();
        lcd.print("System Starting");
        delay(2000);
        updateDisplay();
    }

    Serial.println("Ready. Commands: mo=main open, mc=main close, go=garage open, gc=garage close, l=lock, u=unlock");
}

void loop()
{
    // Check for serial commands
    handleSerialCommands();

    // Handle keypad input
    handleKeypadInput();

    // Monitor gas levels
    checkGasLevels();

    // Handle IR sensors (only if system is unlocked and no gas alarm)
    if (!systemLocked && !gasAlarmActive)
    {
        handleIRSensors();
    }

    // Update display every second
    if (millis() - lastDisplayUpdate > 1000)
    {
        updateDisplay();
        lastDisplayUpdate = millis();
    }

    delay(500); // Faster loop for better gas sensor responsiveness
}

void handleSerialCommands()
{
    if (Serial.available() > 0)
    {
        String command = Serial.readString();
        command.trim();

        if (command == "mo")
        {
            if (!mainDoorIsOpen)
            {
                Serial.println("Manual main door open");
                manualOpenMainDoor();
                showStatusMessage("Main Door Open");
            }
        }
        else if (command == "mc")
        {
            if (mainDoorIsOpen)
            {
                Serial.println("Manual main door close");
                closeMainDoor();
                showStatusMessage("Main Door Close");
            }
        }
        else if (command == "go")
        {
            if (!garageDoorIsOpen)
            {
                Serial.println("Manual garage door open");
                manualOpenGarageDoor();
                showStatusMessage("Garage Door Open");
            }
        }
        else if (command == "gc")
        {
            if (garageDoorIsOpen)
            {
                Serial.println("Manual garage door close");
                closeGarageDoor();
                showStatusMessage("Garage Door Close");
            }
        }
        else if (command == "l")
        {
            systemLocked = true;
            Serial.println("System locked");
            showStatusMessage("System Locked");
        }
        else if (command == "u")
        {
            systemLocked = false;
            Serial.println("System unlocked");
            showStatusMessage("System Unlocked");
        }
    }
}

void handleKeypadInput()
{
    char key = keypad.getKey();
    if (key)
    {
        Serial.print("Key: ");
        Serial.println(key);

        if (key == '#')
        {
            if (inputPassword == correctPassword)
            {
                showStatusMessage("Access Granted");
                Serial.println("Access granted");
                openMainDoor();
                inputPassword = "";
                attemptCount = 0;
            }
            else
            {
                attemptCount++;
                showStatusMessage("Wrong Password");
                Serial.println("Wrong password");
                inputPassword = "";
                if (attemptCount >= maxAttempts)
                {
                    triggerSecurityAlarm();
                }
            }
        }
        else if (key == '*')
        {
            inputPassword = "";
            showStatusMessage("Password Reset");
            Serial.println("Password cleared");
        }
        else if (inputPassword.length() < 4 && isDigit(key))
        {
            inputPassword += key;
        }
    }
}

void checkGasLevels()
{
    mq2Value = analogRead(mq2Pin);
    Serial.print("MQ2 Value: ");
    Serial.println(mq2Value);

    if (mq2Value > gasThreshold)
    {
        if (!gasAlarmActive)
        {
            gasAlarmActive = true;
            Serial.println("GAS LEAK DETECTED!");
            showStatusMessage("High Gas Level");
        }

        // Start or maintain buzzer
        if (!buzzerActive)
        {
            digitalWrite(buzzerPin, HIGH);
            buzzerStartTime = millis();
            buzzerActive = true;
        }

        // Check if 10 seconds have passed for buzzer auto-stop
        if (buzzerActive && (millis() - buzzerStartTime >= buzzDuration))
        {
            digitalWrite(buzzerPin, LOW);
            buzzerActive = false;
        }
    }
    else
    {
        // Gas levels back to normal
        if (gasAlarmActive)
        {
            gasAlarmActive = false;
            showStatusMessage("Air Status Good");
            Serial.println("Gas levels normal");
        }

        // Reset buzzer if condition goes back to normal
        if (buzzerActive)
        {
            digitalWrite(buzzerPin, LOW);
            buzzerActive = false;
        }
    }
}

void handleIRSensors()
{
    outsideSensorValue = digitalRead(outsideIrSensorPin);
    insideSensorValue = digitalRead(insideIrSensorPin);

    // Handle outside sensor
    if (outsideSensorValue == LOW && !outsideObjectDetected && !garageDoorIsOpen)
    {
        Serial.println("Outside sensor triggered");
        outsideObjectDetected = true;
        showStatusMessage("Garage Door Open");
        autoOpenGarageDoor();
    }

    // Handle inside sensor
    if (insideSensorValue == LOW && !insideObjectDetected && !garageDoorIsOpen)
    {
        Serial.println("Inside sensor triggered");
        insideObjectDetected = true;
        showStatusMessage("Garage Door Open");
        autoOpenGarageDoor();
    }

    // Reset sensor states
    if (outsideSensorValue == HIGH)
    {
        outsideObjectDetected = false;
    }
    if (insideSensorValue == HIGH)
    {
        insideObjectDetected = false;
    }
}

void updateDisplay()
{
    // Check if status message timeout has passed
    if (showingStatus && (millis() - statusMessageTime > 3000))
    {
        showingStatus = false;
    }

    lcd.clear();

    if (gasAlarmActive)
    {
        lcd.print("!!! GAS ALERT !!!");
        lcd.setCursor(0, 1);
        lcd.print("Level: ");
        lcd.print(mq2Value);
        lcd.print("  ");
    }
    else if (showingStatus)
    {
        // Show status message centered on first line
        int padding = (16 - statusMessage.length()) / 2;
        for (int i = 0; i < padding; i++)
            lcd.print(" ");
        lcd.print(statusMessage);
        lcd.setCursor(0, 1);
        if (inputPassword.length() > 0)
        {
            lcd.print("Pass: ");
            for (int i = 0; i < inputPassword.length(); i++)
            {
                lcd.print("*");
            }
        }
        else
        {
            lcd.print("                "); // Clear second line
        }
    }
    else if (inputPassword.length() > 0)
    {
        lcd.print("Enter Password:");
        lcd.setCursor(0, 1);
        for (int i = 0; i < inputPassword.length(); i++)
        {
            lcd.print("*");
        }
    }
    else
    {
        // Normal status display
        lcd.print("M:");
        lcd.print(mainDoorIsOpen ? "O" : "C");
        lcd.print(" G:");
        lcd.print(garageDoorIsOpen ? "O" : "C");
        lcd.print(" ");
        lcd.print(systemLocked ? "L" : "U");
        lcd.setCursor(0, 1);
        lcd.print("Gas: ");
        lcd.print(mq2Value);
        lcd.print(" ");
        if (mq2Value > gasThreshold)
        {
            lcd.print("HIGH");
        }
        else
        {
            lcd.print("OK  ");
        }
    }
}

void showStatusMessage(String message)
{
    statusMessage = message;
    showingStatus = true;
    statusMessageTime = millis();
}

// Main Door Functions (180° servo)
void autoOpenMainDoor()
{
    mainDoorServo.write(90); // Open main door
    mainDoorIsOpen = true;
    delay(3000); // Keep open for 3 seconds
    showStatusMessage("Main Door Closed");
    closeMainDoor();
}

void manualOpenMainDoor()
{
    mainDoorServo.write(90); // Open main door
    mainDoorIsOpen = true;
}

void openMainDoor()
{
    mainDoorServo.write(90); // Unlock/open main door
    mainDoorIsOpen = true;
    showStatusMessage("Main Door Open");
    delay(3000); // Keep open for 3 seconds
    showStatusMessage("Main Door Closed");
    closeMainDoor();
}

void closeMainDoor()
{
    mainDoorServo.write(0); // Close/lock main door
    mainDoorIsOpen = false;
    delay(1000);
}

// Garage Door Functions (360° servo)
void autoOpenGarageDoor()
{
    // Open garage door (360° servo - forward rotation)
    garageDoorServo.write(180); // Full speed forward
    delay(3000);                // Run for 3 seconds to open
    garageDoorServo.write(90);  // Stop
    garageDoorIsOpen = true;

    delay(5000); // Keep open for 5 seconds

    showStatusMessage("Garage Door Closed");
    closeGarageDoor();
}

void manualOpenGarageDoor()
{
    // Open garage door manually
    garageDoorServo.write(180); // Full speed forward
    delay(3000);                // Run for 3 seconds to open
    garageDoorServo.write(90);  // Stop
    garageDoorIsOpen = true;
}

void closeGarageDoor()
{
    // Close garage door (360° servo - reverse rotation)
    garageDoorServo.write(0);  // Full speed reverse
    delay(3000);               // Run for 3 seconds to close
    garageDoorServo.write(90); // Stop
    garageDoorIsOpen = false;
    delay(1000);
}

void triggerSecurityAlarm()
{
    showStatusMessage("INTRUDER ALERT!");
    Serial.println("INTRUDER ALERT!");

    // Sound security alarm (different pattern from gas alarm)
    for (int i = 0; i < 10; i++)
    {
        digitalWrite(buzzerPin, HIGH);
        delay(300);
        digitalWrite(buzzerPin, LOW);
        delay(300);
    }

    // Lockout period
    showStatusMessage("Locked 10 sec");
    delay(10000);

    // Reset
    inputPassword = "";
    attemptCount = 0;
    showStatusMessage("System Ready");
}

void triggerGasAlarm()
{
    // This function is now handled by checkGasLevels()
    // Keeping for compatibility but functionality moved to checkGasLevels()
}