import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.NEXT_SUPABASE_SERVICE_ROLE_KEY

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function examineFanControl() {
  console.log('🔍 Examining fan_control table to understand the pattern...\n')
  
  try {
    // Get fan_control table data
    const { data: fanControls, error: fanError } = await supabase
      .from('fan_control')
      .select('*')
    
    if (fanError) {
      console.error('Error fetching fan_control:', fanError)
      return
    }
    
    console.log('📊 fan_control table contents:')
    fanControls.forEach((record, index) => {
      console.log(`\n   Record ${index + 1}:`)
      Object.entries(record).forEach(([key, value]) => {
        console.log(`      ${key}: ${JSON.stringify(value)}`)
      })
    })
    
    // Get corresponding devices
    const deviceIds = fanControls.map(fc => fc.device_id)
    const { data: devices, error: deviceError } = await supabase
      .from('devices')
      .select('*')
      .in('id', deviceIds)
    
    if (!deviceError && devices) {
      console.log('\n📱 Corresponding devices:')
      devices.forEach((device, index) => {
        console.log(`\n   Device ${index + 1}:`)
        console.log(`      Name: ${device.name}`)
        console.log(`      Type: ${device.type}`)
        console.log(`      Properties: ${JSON.stringify(device.properties, null, 6)}`)
        console.log(`      Current State: ${JSON.stringify(device.current_state, null, 6)}`)
      })
    }
    
    // Compare with other systems that don't have tables
    console.log('\n🔍 Comparing with lighting system (no dedicated table):')
    
    const { data: lightingDevices, error: lightingError } = await supabase
      .from('devices')
      .select(`
        *,
        systems (name, type)
      `)
      .eq('systems.type', 'lighting')
    
    if (!lightingError && lightingDevices) {
      console.log(`\n   Found ${lightingDevices.length} lighting devices:`)
      lightingDevices.forEach((device, index) => {
        console.log(`\n   Device ${index + 1}:`)
        console.log(`      Name: ${device.name}`)
        console.log(`      Type: ${device.type}`)
        console.log(`      Properties: ${JSON.stringify(device.properties, null, 6)}`)
        console.log(`      Current State: ${JSON.stringify(device.current_state, null, 6)}`)
      })
    }
    
    console.log('\n💡 Analysis - When does a system need a dedicated table?')
    console.log('\n   fan_control table contains:')
    console.log('   • System-wide configuration (auto_mode, target_temperature)')
    console.log('   • Complex settings (temperature_thresholds, speed_settings)')
    console.log('   • System state that spans multiple devices')
    console.log('   • Historical/operational data (current_speed, current_temperature)')
    
    console.log('\n   Lighting system (no table) uses:')
    console.log('   • Device-specific properties in devices.properties')
    console.log('   • Device-specific state in devices.current_state')
    console.log('   • No system-wide configuration needed')
    
    console.log('\n🎯 Pattern Conclusion:')
    console.log('   Systems need dedicated tables when they have:')
    console.log('   ✅ System-wide configuration that affects multiple devices')
    console.log('   ✅ Complex operational parameters and thresholds')
    console.log('   ✅ System state that needs to be tracked independently')
    console.log('   ✅ Historical data or analytics requirements')
    console.log('')
    console.log('   Systems DON\'T need dedicated tables when:')
    console.log('   ❌ Each device operates independently')
    console.log('   ❌ Configuration is device-specific only')
    console.log('   ❌ No system-wide state tracking needed')
    
  } catch (error) {
    console.error('❌ Examination failed:', error.message)
  }
}

examineFanControl().catch(console.error)
